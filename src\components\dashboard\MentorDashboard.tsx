'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Star,
  Activity,
  Newspaper,
  BarChart,
  AlertTriangle,
  School,
  Users,
  TrendingUp,
  Eye,
  PlusCircle,
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import MentorAnalytics from './MentorAnalytics'
import SystemicIssueForm from './SystemicIssueForm'
import TeacherReviewsTable from './TeacherReviewsTable'
import { isMediaPath } from '@/lib/media-utils'
import MentorReviewModal from './MentorReviewModal'
import { getImageUrl } from '@/utils/imageUtils'

interface User {
  id: string
  email: string
  firstName?: string
  lastName?: string
  role?:
    | {
        id: string
        name: string
        slug: string
      }
    | string
  school?:
    | {
        id: string
        name: string
      }
    | string
}

interface MentorDashboardProps {
  user: User | null
}

export default function MentorDashboard({ user }: MentorDashboardProps) {
  const router = useRouter()
  const [reviewQuality, setReviewQuality] = useState<number>(0)
  const [engagementTimeline, setEngagementTimeline] = useState<any[]>([])
  const [news, setNews] = useState<any[]>([])
  const [publicNews, setPublicNews] = useState<any[]>([])
  const [teachers, setTeachers] = useState<any[]>([])
  const [pendingReviews, setPendingReviews] = useState<any[]>([])
  const [recentTeacherReviews, setRecentTeacherReviews] = useState<any[]>([])
  const [articleQualityTrends, setArticleQualityTrends] = useState<any[]>([])
  const [schoolEngagementMetrics, setSchoolEngagementMetrics] = useState<any[]>([])
  const [selectedTeacherReview, setSelectedTeacherReview] = useState<any>(null)
  const [isReviewModalOpen, setIsReviewModalOpen] = useState(false)
  const [selectedReviewForModal, setSelectedReviewForModal] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    async function fetchData() {
      try {
        setIsLoading(true)

        // Fetch review quality data
        const reviewQualityResponse = await fetch('/api/dashboard/mentor/review-quality', {
          credentials: 'include',
        })
        const reviewQualityData = await reviewQualityResponse.json()
        setReviewQuality(reviewQualityData.reviewQuality)

        // Fetch engagement timeline data
        const engagementTimelineResponse = await fetch(
          '/api/dashboard/mentor/engagement-timeline',
          {
            credentials: 'include',
          },
        )
        const engagementTimelineData = await engagementTimelineResponse.json()
        setEngagementTimeline(engagementTimelineData.engagementTimeline)

        // Fetch school news data
        const newsResponse = await fetch('/api/dashboard/news', {
          credentials: 'include',
        })
        const newsData = await newsResponse.json()
        setNews(newsData.news || newsData.data?.news || [])

        // Fetch public news data
        try {
          const publicNewsResponse = await fetch('/api/news?limit=5', {
            credentials: 'include',
          })

          if (publicNewsResponse.ok) {
            const publicNewsData = await publicNewsResponse.json()
            setPublicNews(publicNewsData.docs || [])
          } else {
            console.error('Failed to fetch public news:', await publicNewsResponse.text())
          }
        } catch (publicNewsError) {
          console.error('Error fetching public news:', publicNewsError)
          // Continue even if public news fetch fails
        }

        // Fetch teachers data
        try {
          const teachersResponse = await fetch('/api/dashboard/teachers', {
            credentials: 'include',
          })

          if (teachersResponse.ok) {
            const teachersData = await teachersResponse.json()
            setTeachers(teachersData.teachers || [])
          } else {
            console.error('Failed to fetch teachers:', await teachersResponse.text())
          }
        } catch (teachersError) {
          console.error('Error fetching teachers:', teachersError)
          // Continue even if teachers fetch fails
        }

        // Fetch pending reviews data
        try {
          const pendingReviewsResponse = await fetch('/api/dashboard/mentor/pending-reviews', {
            credentials: 'include',
          })

          if (pendingReviewsResponse.ok) {
            const pendingReviewsData = await pendingReviewsResponse.json()
            // Filter out any reviews with media path IDs
            const filteredReviews =
              pendingReviewsData.reviews?.filter(
                (review: any) => review.id && !isMediaPath(review.id),
              ) || []
            setPendingReviews(filteredReviews)
          } else {
            console.error('Failed to fetch pending reviews:', await pendingReviewsResponse.text())
          }
        } catch (pendingReviewsError) {
          console.error('Error fetching pending reviews:', pendingReviewsError)
          // Continue even if pending reviews fetch fails
        }

        // Fetch recent teacher reviews
        try {
          const recentReviewsResponse = await fetch(
            '/api/dashboard/mentor/recent-teacher-reviews',
            {
              credentials: 'include',
            },
          )

          if (recentReviewsResponse.ok) {
            const recentReviewsData = await recentReviewsResponse.json()
            // Filter out any reviews with media path IDs
            const filteredReviews =
              recentReviewsData.reviews?.filter(
                (review: any) => review.id && !isMediaPath(review.id),
              ) || []
            setRecentTeacherReviews(filteredReviews)
          } else {
            console.error(
              'Failed to fetch recent teacher reviews:',
              await recentReviewsResponse.text(),
            )
          }
        } catch (recentReviewsError) {
          console.error('Error fetching recent teacher reviews:', recentReviewsError)
          // Continue even if recent reviews fetch fails
        }

        // Fetch article quality trends
        try {
          const articleQualityResponse = await fetch('/api/dashboard/mentor/article-quality', {
            credentials: 'include',
          })

          if (articleQualityResponse.ok) {
            const articleQualityData = await articleQualityResponse.json()
            setArticleQualityTrends(articleQualityData.trends || [])
          } else {
            console.error(
              'Failed to fetch article quality trends:',
              await articleQualityResponse.text(),
            )
          }
        } catch (articleQualityError) {
          console.error('Error fetching article quality trends:', articleQualityError)
          // Continue even if article quality trends fetch fails
        }

        // Fetch school engagement metrics
        try {
          const schoolEngagementResponse = await fetch('/api/dashboard/mentor/school-engagement', {
            credentials: 'include',
          })

          if (schoolEngagementResponse.ok) {
            const schoolEngagementData = await schoolEngagementResponse.json()
            setSchoolEngagementMetrics(
              schoolEngagementData.metrics || schoolEngagementData.schools || [],
            )
          } else {
            console.error(
              'Failed to fetch school engagement metrics:',
              await schoolEngagementResponse.text(),
            )
          }
        } catch (schoolEngagementError) {
          console.error('Error fetching school engagement metrics:', schoolEngagementError)
          // Continue even if school engagement metrics fetch fails
        }

        setIsLoading(false)
      } catch (err) {
        console.error('Error fetching mentor dashboard data:', err)
        setError('فشل تحميل بيانات لوحة التحكم الخاصة بالموجه. يرجى المحاولة مرة أخرى.')
        setIsLoading(false)
      }
    }

    fetchData()
  }, [])

  const handleOpenReviewModal = (review: any) => {
    setSelectedReviewForModal(review)
    setIsReviewModalOpen(true)
  }

  const handleCloseReviewModal = () => {
    setIsReviewModalOpen(false)
    setSelectedReviewForModal(null)
  }

  const handleReviewSuccess = () => {
    // Refresh pending reviews data
    fetchPendingReviews()
  }

  const fetchPendingReviews = async () => {
    try {
      const pendingReviewsResponse = await fetch('/api/dashboard/mentor/pending-reviews', {
        credentials: 'include',
      })

      if (pendingReviewsResponse.ok) {
        const pendingReviewsData = await pendingReviewsResponse.json()
        // Filter out any reviews with media path IDs
        const filteredReviews =
          pendingReviewsData.reviews?.filter(
            (review: any) => review.id && !isMediaPath(review.id),
          ) || []
        setPendingReviews(filteredReviews)
      } else {
        console.error('Failed to fetch pending reviews:', await pendingReviewsResponse.text())
      }
    } catch (pendingReviewsError) {
      console.error('Error fetching pending reviews:', pendingReviewsError)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div
        dir="rtl"
        className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4"
      >
        {error}
      </div>
    )
  }

  // Get school ID from user
  const schoolId = user?.school
    ? typeof user.school === 'object'
      ? user.school.id
      : user.school
    : undefined

  return (
    <div dir="rtl" className="p-6">
      <h1 className="text-3xl font-bold mb-4">لوحة تحكم الموجه</h1>
      <p className="text-gray-500">مرحباً بك مرة أخرى، {user?.firstName || 'الموجه'}</p>

      <Tabs dir="rtl" defaultValue="overview" className="mt-8">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center">
            <Activity className="ml-2 h-4 w-4" />
            نظرة عامة
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center">
            <BarChart className="ml-2 h-4 w-4" />
            التحليلات
          </TabsTrigger>
          <TabsTrigger value="actions" className="flex items-center">
            <AlertTriangle className="ml-2 h-4 w-4" />
            الإجراءات
          </TabsTrigger>
          <TabsTrigger value="news" className="flex items-center">
            <Newspaper className="ml-2 h-4 w-4" />
            الأخبار
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Review Quality */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center text-sm font-medium">
                  <Star className="ml-2 h-4 w-4" />
                  جودة المراجعة
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-2xl font-bold">{reviewQuality}%</p>
                <p className="text-xs text-gray-500">متوسط درجة جودة المراجعة</p>
              </CardContent>
            </Card>

            {/* Teacher Reviews */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center text-sm font-medium">
                  <Users className="ml-2 h-4 w-4" />
                  مراجعات المعلمين
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-2xl font-bold">{recentTeacherReviews.length}</p>
                <p className="text-xs text-gray-500">مراجعات المعلمين الأخيرة</p>
                <Button
                  variant="link"
                  className="p-0 mt-2 text-xs"
                  onClick={() => router.push('/dashboard/teachers')}
                >
                  عرض الكل
                </Button>
              </CardContent>
            </Card>

            {/* Article Quality */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center text-sm font-medium">
                  <TrendingUp className="ml-2 h-4 w-4" />
                  جودة المقال
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-2xl font-bold">
                  {articleQualityTrends.length > 0
                    ? (
                        articleQualityTrends.reduce((sum, trend) => sum + trend.averageRating, 0) /
                        articleQualityTrends.length
                      ).toFixed(1)
                    : '0.0'}
                </p>
                <p className="text-xs text-gray-500">متوسط تقييم المقال</p>
                <Button
                  variant="link"
                  className="p-0 mt-2 text-xs"
                  onClick={() => router.push('/dashboard/articles')}
                >
                  عرض المقالات
                </Button>
              </CardContent>
            </Card>

            {/* School Engagement */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center text-sm font-medium">
                  <School className="ml-2 h-4 w-4" />
                  مشاركة المدرسة
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-2xl font-bold">
                  {schoolEngagementMetrics.length > 0
                    ? schoolEngagementMetrics.reduce(
                        (sum, school) =>
                          sum + (school.activeTeachers || 0) + (school.activeStudents || 0),
                        0,
                      )
                    : 0}
                </p>
                <p className="text-xs text-gray-500">المستخدمين النشطين</p>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Pending Teacher Reviews */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Star className="ml-2 h-5 w-5 text-yellow-500" />
                  مراجعات المعلمين المنتظرة للتقييم
                </CardTitle>
              </CardHeader>
              <CardContent>
                {pendingReviews.length > 0 ? (
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>المعلم</TableHead>
                          <TableHead>المقال</TableHead>
                          <TableHead>التقييم</TableHead>
                          <TableHead>الإجراءات</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {pendingReviews.slice(0, 5).map((review, index) => (
                          <TableRow key={review.id || `review-${index}`}>
                            <TableCell className="font-medium">
                              {review.teacherName || 'معلم غير معروف'}
                            </TableCell>
                            <TableCell>{review.articleTitle || 'مقال غير معروف'}</TableCell>
                            <TableCell>
                              <div className="flex items-center">
                                <Star className="h-4 w-4 text-yellow-500 ml-1" />
                                <span>{review.rating}/10</span>
                              </div>
                            </TableCell>
                            <TableCell>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleOpenReviewModal(review)}
                              >
                                <Eye className="h-4 w-4 ml-1" />
                                تقييم
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                    {pendingReviews.length > 5 && (
                      <div className="mt-4 text-center">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => router.push('/dashboard/mentor/pending-reviews')}
                        >
                          عرض الكل ({pendingReviews.length})
                        </Button>
                      </div>
                    )}
                  </div>
                ) : (
                  <p className="text-gray-500">لا توجد مراجعات معلمين منتظرة للتقييم.</p>
                )}
              </CardContent>
            </Card>

            {/* News Publication Hub */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Newspaper className="ml-2 h-5 w-5 text-blue-500" />
                  مركز نشر الأخبار
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-500 mb-4">إدارة ونشر أخبار المدرسة</p>
                <div className="flex flex-col space-y-2">
                  <Button onClick={() => router.push('/dashboard/news/create')} className="w-full">
                    <PlusCircle className="ml-2 h-4 w-4" />
                    إنشاء مقال إخباري
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => router.push('/dashboard/news')}
                    className="w-full"
                  >
                    <Newspaper className="ml-2 h-4 w-4" />
                    إدارة الأخبار
                  </Button>
                </div>

                {news.length > 0 && (
                  <div className="mt-4">
                    <h3 className="text-sm font-medium mb-2">الأخبار الأخيرة</h3>
                    <ul className="space-y-2">
                      {news.slice(0, 3).map((item, index) => (
                        <li key={item.id || `news-${index}`} className="text-sm">
                          <Link href={`/news/${item.id}`} className="text-blue-600 hover:underline">
                            {item.title}
                          </Link>
                          <span className="text-xs text-gray-500 mr-2">
                            {new Date(item.createdAt).toLocaleDateString('ar-EG')}
                          </span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-6">
          {schoolId ? (
            <>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">إجمالي المعلمين</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{teachers.length}</div>
                    <p className="text-xs text-gray-500">المعلمون النشطون في مدرستك</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">متوسط معدل الموافقة</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {teachers.length > 0
                        ? Math.round(
                            (teachers.reduce(
                              (sum, teacher) => sum + (teacher.stats?.approvalRate || 0),
                              0,
                            ) /
                              teachers.length) *
                              100,
                          )
                        : 0}
                      %
                    </div>
                    <p className="text-xs text-gray-500">معدل موافقة المعلمين</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">المقالات المراجعة</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {teachers.reduce(
                        (sum, teacher) => sum + (teacher.stats?.articlesReviewed || 0),
                        0,
                      )}
                    </div>
                    <p className="text-xs text-gray-500">المقالات المراجعة بالكامل</p>
                  </CardContent>
                </Card>
              </div>

              {/* Teacher Review Analytics */}
              <MentorAnalytics schoolId={schoolId} />

              {/* Recent Teacher Reviews */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Star className="ml-2 h-5 w-5 text-yellow-500" />
                    مراجعات المعلمين الأخيرة
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {recentTeacherReviews.length > 0 ? (
                    <div className="overflow-x-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>المعلم</TableHead>
                            <TableHead>المقال</TableHead>
                            <TableHead>التقييم</TableHead>
                            <TableHead>الحالة</TableHead>
                            <TableHead>التاريخ</TableHead>
                            <TableHead>الإجراءات</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {recentTeacherReviews.slice(0, 5).map((review, index) => (
                            <TableRow key={review.id || `review-${index}`}>
                              <TableCell className="font-medium">
                                {review.teacherName || 'معلم غير معروف'}
                                <div className="text-xs text-gray-500">
                                  <Badge variant="outline">
                                    معرف المعلم: {review.teacherId?.substring(0, 6) || 'غير معروف'}
                                  </Badge>
                                </div>
                              </TableCell>
                              <TableCell>{review.articleTitle || 'مقال غير معروف'}</TableCell>
                              <TableCell>
                                <div className="flex items-center">
                                  <Star className="h-4 w-4 text-yellow-500 ml-1" />
                                  <span>{review.rating}/10</span>
                                </div>
                              </TableCell>
                              <TableCell>
                                {review.approved ? (
                                  <Badge variant="default" className="bg-green-100 text-green-800">
                                    موافق
                                  </Badge>
                                ) : (
                                  <Badge variant="destructive" className="bg-red-100 text-red-800">
                                    مرفوض
                                  </Badge>
                                )}
                              </TableCell>
                              <TableCell>
                                {review.createdAt
                                  ? new Date(review.createdAt).toLocaleDateString('ar-EG')
                                  : 'غير معروف'}
                              </TableCell>
                              <TableCell>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() =>
                                    router.push(
                                      `/dashboard/mentor/review-feedback/${review.articleId}/${review.reviewIndex || 0}`,
                                    )
                                  }
                                >
                                  <Eye className="h-4 w-4 ml-1" />
                                  تقييم
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  ) : (
                    <p className="text-gray-500">لا توجد مراجعات معلمين حديثة.</p>
                  )}
                </CardContent>
              </Card>

              {/* Teachers Overview */}
              <Card>
                <CardHeader>
                  <CardTitle>نظرة عامة على المعلمين</CardTitle>
                </CardHeader>
                <CardContent>
                  {teachers.length > 0 ? (
                    <div className="overflow-x-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>الاسم</TableHead>
                            <TableHead>البريد الإلكتروني</TableHead>
                            <TableHead>المقالات المراجعة</TableHead>
                            <TableHead>معدل الموافقة</TableHead>
                            <TableHead>طلاب موافقين</TableHead>
                            <TableHead>الإجراءات</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {teachers.map((teacher) => (
                            <TableRow key={teacher.id} className="hover:bg-gray-50">
                              <TableCell className="font-medium">
                                {teacher.firstName} {teacher.lastName}
                                <div className="text-xs text-gray-500">
                                  <Badge variant="outline">
                                    معرف: {teacher.id.substring(0, 6)}
                                  </Badge>
                                </div>
                              </TableCell>
                              <TableCell>{teacher.email}</TableCell>
                              <TableCell>{teacher.stats?.articlesReviewed || 0}</TableCell>
                              <TableCell>
                                {teacher.stats?.approvalRate
                                  ? Math.round(teacher.stats.approvalRate * 100) + '%'
                                  : '0%'}
                              </TableCell>
                              <TableCell>{teacher.stats?.studentsApproved || 0}</TableCell>
                              <TableCell>
                                <div className="flex space-x-2">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => router.push(`/dashboard/teacher/${teacher.id}`)}
                                  >
                                    عرض التفاصيل
                                  </Button>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() =>
                                      router.push(
                                        `/dashboard/teachers/activities?teacherId=${teacher.id}`,
                                      )
                                    }
                                  >
                                    الأنشطة
                                  </Button>
                                </div>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  ) : (
                    <p className="text-gray-500">لا توجد معلمين لهذه المدرسة.</p>
                  )}
                </CardContent>
              </Card>
            </>
          ) : (
            <div className="bg-amber-100 border border-amber-400 text-amber-700 px-4 py-3 rounded mb-4">
              يجب أن تكون مرتبطًا بمدرسة لعرض التحليلات.
            </div>
          )}
        </TabsContent>

        {/* Actions Tab */}
        <TabsContent value="actions" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Systemic Issue Form */}
            {schoolId ? (
              <SystemicIssueForm schoolId={schoolId} />
            ) : (
              <div className="bg-amber-100 border border-amber-400 text-amber-700 px-4 py-3 rounded mb-4">
                يجب أن تكون مرتبطًا بمدرسة لتحديد مشكلات النظام.
              </div>
            )}

            {/* News Publication Card */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Newspaper className="ml-2 h-5 w-5 text-blue-500" />
                  نشر أخبار المدرسة
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-500 mb-4">
                  إنشاء ونشر مقالات إخبارية لمجتمع مدرستك.
                </p>
                <div className="flex flex-col space-y-2">
                  <Button onClick={() => router.push('/dashboard/news/create')}>
                    <PlusCircle className="ml-2 h-4 w-4" />
                    إنشاء مقال إخباري
                  </Button>
                  <Button variant="outline" onClick={() => router.push('/dashboard/news')}>
                    <Newspaper className="ml-2 h-4 w-4" />
                    إدارة الأخبار
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Teacher Review Rating */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Star className="ml-2 h-5 w-5 text-yellow-500" />
                تقييم مراجعات المعلمين
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-500 mb-4">
                تقييم مراجعات المعلمين للحفاظ على معايير الجودة وتقديم التعليقات
              </p>

              {pendingReviews.length > 0 ? (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>المعلم</TableHead>
                        <TableHead>المقال</TableHead>
                        <TableHead>التقييم</TableHead>
                        <TableHead>الحالة</TableHead>
                        <TableHead>الإجراءات</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {pendingReviews.slice(0, 5).map((review, index) => (
                        <TableRow key={review.id || `pending-review-${index}`}>
                          <TableCell className="font-medium">
                            {review.teacherName || 'معلم غير معروف'}
                            <div className="text-xs text-gray-500">
                              <Badge variant="outline">
                                معرف المعلم: {review.teacherId?.substring(0, 6) || 'غير معروف'}
                              </Badge>
                            </div>
                          </TableCell>
                          <TableCell>{review.articleTitle || 'مقال غير معروف'}</TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <Star className="h-4 w-4 text-yellow-500 ml-1" />
                              <span>{review.rating}/10</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            {review.approved ? (
                              <Badge variant="default" className="bg-green-100 text-green-800">
                                موافق
                              </Badge>
                            ) : (
                              <Badge variant="destructive" className="bg-red-100 text-red-800">
                                مرفوض
                              </Badge>
                            )}
                          </TableCell>
                          <TableCell>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleOpenReviewModal(review)}
                            >
                              <Eye className="h-4 w-4 ml-1" />
                              تقييم
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>

                  {pendingReviews.length > 5 && (
                    <div className="mt-4 text-center">
                      <Button
                        variant="outline"
                        onClick={() => router.push('/dashboard/mentor/pending-reviews')}
                      >
                        عرض الكل ({pendingReviews.length})
                      </Button>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-8 border rounded-md bg-gray-50">
                  <p className="text-gray-500">لا توجد مراجعات معلمين منتظرة للتقييم.</p>
                  <Button
                    variant="outline"
                    className="mt-4"
                    onClick={() => router.push('/dashboard/teachers')}
                  >
                    عرض كل المعلمين
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Teacher Activities Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Users className="ml-2 h-5 w-5 text-indigo-500" />
                أنشطة المعلمين
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-500 mb-4">
                تقييم وتصنيف الأنشطة التعليمية لتقديم التعليقات وتحسين جودة التدريس
              </p>
              <Button
                className="w-full"
                onClick={() => router.push('/dashboard/teachers/activities')}
              >
                <Eye className="ml-2 h-4 w-4" />
                عرض أنشطة المعلمين
              </Button>
            </CardContent>
          </Card>

          {/* Performance Comparison */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BarChart className="ml-2 h-5 w-5 text-blue-500" />
                مقارنة الأداء
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-500 mb-4">
                مقارنة مقاييس أداء المعلمين لتحديد مناطق التحسين
              </p>

              {teachers.length > 0 ? (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>المعلم</TableHead>
                        <TableHead>المقالات المراجعة</TableHead>
                        <TableHead>التقييم المتوسط</TableHead>
                        <TableHead>معدل الموافقة</TableHead>
                        <TableHead>وقت الاستجابة</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {teachers.slice(0, 5).map((teacher) => (
                        <TableRow key={`perf-${teacher.id}`} className="hover:bg-gray-50">
                          <TableCell className="font-medium">
                            {teacher.firstName} {teacher.lastName}
                          </TableCell>
                          <TableCell>{teacher.stats?.articlesReviewed || 0}</TableCell>
                          <TableCell>
                            {teacher.stats?.averageRating
                              ? teacher.stats.averageRating.toFixed(1) + '/10'
                              : 'غير متاح'}
                          </TableCell>
                          <TableCell>
                            {teacher.stats?.approvalRate
                              ? Math.round(teacher.stats.approvalRate * 100) + '%'
                              : '0%'}
                          </TableCell>
                          <TableCell>
                            {teacher.stats?.averageResponseTime
                              ? teacher.stats.averageResponseTime + ' ساعات'
                              : 'غير متاح'}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              ) : (
                <p className="text-gray-500">لا توجد بيانات أداء المعلمين.</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* News Tab */}
        <TabsContent value="news" className="space-y-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-2xl font-bold">إدارة الأخبار</h2>
            <Button onClick={() => router.push('/dashboard/news/create')}>
              <PlusCircle className="ml-2 h-4 w-4" />
              إنشاء مقال إخباري
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* School News */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <School className="ml-2 h-4 w-4" />
                  أخبار المدرسة
                </CardTitle>
              </CardHeader>
              <CardContent>
                {news && news.length > 0 ? (
                  <div className="space-y-4">
                    {news.slice(0, 5).map((item: any, index: number) => {
                      // Safely handle image URL using the utility function
                      const imageUrl = item.image ? getImageUrl(item.image) : null

                      return (
                        <div
                          key={item.id || `news-${index}`}
                          className="border-b pb-4 last:border-0"
                        >
                          <div className="flex justify-between items-start">
                            <div className="flex">
                              {imageUrl && (
                                <div className="mr-3 flex-shrink-0">
                                  <img
                                    src={imageUrl}
                                    alt={item.title}
                                    className="w-16 h-16 object-cover rounded"
                                    onError={(e) => {
                                      // Handle image loading errors
                                      e.currentTarget.src = '/images/placeholder.jpg'
                                    }}
                                  />
                                </div>
                              )}
                              <div>
                                <h3 className="font-medium text-lg">{item.title}</h3>
                                <p className="text-sm text-gray-500">
                                  {new Date(item.createdAt).toLocaleDateString('ar-EG')}
                                  {item.status && (
                                    <span
                                      className={`ml-2 ${item.status === 'published' ? 'text-green-600' : 'text-amber-600'}`}
                                    >
                                      • {item.status === 'published' ? 'منشور' : 'مسودة'}
                                    </span>
                                  )}
                                </p>
                              </div>
                            </div>
                            {item.isOwner && (
                              <Badge variant="outline" className="ml-2">
                                منشورك
                              </Badge>
                            )}
                          </div>
                          <p className="mt-2 line-clamp-2">
                            {typeof item.content === 'string'
                              ? item.content.replace(/<[^>]*>/g, '').substring(0, 150) + '...'
                              : 'لا يوجد محتوى'}
                          </p>
                          <div className="flex mt-2 space-x-4">
                            <Button
                              variant="link"
                              className="p-0"
                              onClick={() => router.push(`/news/${item.id}`)}
                            >
                              قراءة المزيد
                            </Button>
                            {item.isOwner && (
                              <Button
                                variant="link"
                                className="p-0 text-blue-600"
                                onClick={() => router.push(`/dashboard/news/edit/${item.id}`)}
                              >
                                تعديل
                              </Button>
                            )}
                          </div>
                        </div>
                      )
                    })}
                  </div>
                ) : (
                  <p className="text-gray-500">لا توجد أخبار مدرستك.</p>
                )}
                <div className="mt-4">
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => router.push('/dashboard/news')}
                  >
                    إدارة أخبار المدرسة
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Public News */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Newspaper className="ml-2 h-4 w-4" />
                  الأخبار العامة
                </CardTitle>
              </CardHeader>
              <CardContent>
                {publicNews && publicNews.length > 0 ? (
                  <div className="space-y-4">
                    {publicNews.map((item: any, index: number) => {
                      // Safely handle image URL using the utility function
                      const imageUrl = item.image ? getImageUrl(item.image) : null

                      return (
                        <div
                          key={item.id || `public-news-${index}`}
                          className="border-b pb-4 last:border-0"
                        >
                          <div className="flex">
                            {imageUrl && (
                              <div className="mr-3 flex-shrink-0">
                                <img
                                  src={imageUrl}
                                  alt={item.title}
                                  className="w-16 h-16 object-cover rounded"
                                  onError={(e) => {
                                    // Handle image loading errors
                                    e.currentTarget.src = '/images/placeholder.jpg'
                                  }}
                                />
                              </div>
                            )}
                            <div>
                              <h3 className="font-medium text-lg">{item.title}</h3>
                              <p className="text-sm text-gray-500">
                                {new Date(item.createdAt).toLocaleDateString('ar-EG')}
                              </p>
                              <p className="mt-2 line-clamp-2">
                                {typeof item.content === 'string'
                                  ? item.content.replace(/<[^>]*>/g, '').substring(0, 150) + '...'
                                  : 'لا يوجد محتوى'}
                              </p>
                              <Button
                                variant="link"
                                className="p-0 ml-2"
                                onClick={() => router.push(`/news/${item.id}`)}
                              >
                                قراءة المزيد
                              </Button>
                            </div>
                          </div>
                        </div>
                      )
                    })}
                  </div>
                ) : (
                  <p className="text-gray-500">لا توجد أخبار عامة.</p>
                )}
                <div className="mt-4">
                  <Button variant="outline" className="w-full" onClick={() => router.push('/news')}>
                    عرض كل الأخبار
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* News Management Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Newspaper className="ml-2 h-5 w-5 text-blue-500" />
                إجراءات إدارة الأخبار
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => router.push('/dashboard/news/create')}
                >
                  <PlusCircle className="ml-2 h-4 w-4" />
                  إنشاء خبر جديد
                </Button>
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => router.push('/dashboard/news?filter=draft')}
                >
                  <Newspaper className="ml-2 h-4 w-4" />
                  إدارة المسودات
                </Button>
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => router.push('/dashboard/news?filter=published')}
                >
                  <Newspaper className="ml-2 h-4 w-4" />
                  الأخبار المنشورة
                </Button>
              </div>

              <div className="mt-6">
                <h3 className="text-sm font-medium mb-2">نصائح لإدارة الأخبار</h3>
                <ul className="list-disc list-inside text-sm text-gray-600 space-y-1">
                  <li>استخدم صور عالية الجودة لتحسين التفاعل</li>
                  <li>احتفظ بعناوين ملائمة ومعلوماتية</li>
                  <li>احفظ المسودات قبل النشر لمراجعة المحتوى</li>
                  <li>استخدم أدوات التنسيق لجعل المحتوى أكثر قابلية للقراءة</li>
                  <li>تضمين علامات مناسبة لتحسين الاكتشاف</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Mentor Review Modal */}
      <MentorReviewModal
        isOpen={isReviewModalOpen}
        onClose={handleCloseReviewModal}
        review={selectedReviewForModal}
        onSuccess={handleReviewSuccess}
      />
    </div>
  )
}
