import { Payload, Where } from 'payload'
// Remove unused imports that are causing errors
// import { User } from "@/payload/payload-types";
// import { getAnonymizedName } from "@/utils/helpers";
// import { getServerSession } from "next-auth";
// import { authOptions } from "../app/api/auth/[...nextauth]/route";
// import { log } from "./logger";

/**
 * Add points to a user based on a specific action
 */
export const addUserPoints = async ({
  payload,
  userId,
  type,
  points,
  description,
  reference = null,
  createActivity = true,
  createNotification = true,
  schoolId = null,
}: {
  payload: Payload
  userId: string
  type: 'article_created' | 'article_published' | 'review_submitted' | 'achievement_earned' | 'rating_received' | 'article_views' | 'news_views' | 'other'
  points: number
  description: string
  reference?: any
  createActivity?: boolean
  createNotification?: boolean
  schoolId?: string | null
}): Promise<number> => {
  let originalUserPoints = 0;
  let activityCreated = false;
  let notificationCreated = false;
  let userUpdated = false;
  
  try {
    // Get the current user to check current points
    const user = await payload.findByID({
      collection: 'users',
      id: userId,
    })
    
    // Save original points in case we need to rollback
    originalUserPoints = user.points || 0;
    
    // Calculate new total points
    const currentPoints = user.points || 0
    const newTotalPoints = currentPoints + points
    
    // Create a new points activity for the user's record
    const pointActivity = {
      type,
      points,
      description,
      reference,
      timestamp: new Date().toISOString(),
    }
    
    // Generate a unique ID for this point transaction to prevent duplicates
    const transactionId = `${userId}_${type}_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    
    // Update the user with new points and activity
    await payload.update({
      collection: 'users',
      id: userId,
      data: {
        points: newTotalPoints,
        pointsActivities: [
          ...(user.pointsActivities || []),
          {
            ...pointActivity as any,
            transactionId // Add transaction ID for deduplication
          }
        ]
      }
    })
    userUpdated = true;
    
    // Determine the user's role and school
    const userRole = typeof user.role === 'object' && user.role !== null 
      ? (user.role.slug || '') 
      : '';
    
    const userSchool = schoolId || (
      typeof user.school === 'object' && user.school !== null 
        ? user.school.id 
        : (user.school || null)
    );
    
    // Create an activity record if requested
    if (createActivity) {
      try {
        // Map the point activity type to an activity type
        let activityType: 'article-review' | 'article-comment' | 'student-approval' | 'profile-image-approval' | 'name-change-approval' | 'news-post' | 'achievement-earned' | 'login' = 'achievement-earned';
        switch (type) {
          case 'article_created':
          case 'article_published':
            activityType = 'article-review';
            break;
          case 'review_submitted':
            activityType = 'article-review';
            break;
          case 'achievement_earned':
            activityType = 'achievement-earned';
            break;
          default:
            activityType = 'achievement-earned';
        }
        
        // Create the activity
        await payload.create({
          collection: 'activities',
          data: {
            userId,
            activityType,
            details: {
              pointsActivity: type,
              description,
              points,
              reference,
              transactionId // Add transaction ID for deduplication
            },
            school: userSchool,
            points,
          },
        })
        activityCreated = true;
      } catch (activityError) {
        console.error('Error creating activity record:', activityError)
        // Continue even if activity creation fails
      }
    }
    
    // Create a notification if requested
    if (createNotification) {
      try {
        // Prepare Arabic message based on point type
        let arabicMessage = '';
        switch (type) {
          case 'article_created':
            arabicMessage = `لقد ربحت ${points} نقطة لإنشاء مقال جديد.`;
            break;
          case 'article_published':
            arabicMessage = `لقد ربحت ${points} نقطة لنشر مقالك.`;
            break;
          case 'review_submitted':
            arabicMessage = `لقد ربحت ${points} نقطة لمراجعة مقال.`;
            break;
          case 'achievement_earned':
            arabicMessage = `تهانينا! لقد ربحت ${points} نقطة لإنجاز جديد: ${description}`;
            break;
          case 'rating_received':
            arabicMessage = `لقد ربحت ${points} نقطة لتقييم إيجابي.`;
            break;
          case 'article_views':
            arabicMessage = `لقد ربحت ${points} نقطة لمشاهدات مقالك.`;
            break;
          case 'news_views':
            arabicMessage = `لقد ربحت ${points} نقطة لمشاهدات منشور الأخبار.`;
            break;
          default:
            arabicMessage = `لقد ربحت ${points} نقطة: ${description}`;
        }

        await payload.create({
          collection: 'notifications',
          data: {
            user: userId,
            message: arabicMessage,
            type: 'success',
            read: false,
            details: {
              pointsActivity: type,
              reference,
              pointsChange: points,
              transactionId // Add transaction ID for deduplication
            },
          },
        })
        notificationCreated = true;
      } catch (notificationError) {
        console.error('Error creating notification:', notificationError)
        // Continue even if notification creation fails
      }
    }
    
    // Return the new total points
    return newTotalPoints
  } catch (error) {
    console.error('Error adding user points:', error)
    
    // Attempt to rollback changes if we updated the user but failed later
    if (userUpdated) {
      try {
        console.log(`Rolling back points for user ${userId} to ${originalUserPoints}`);
        // Roll back to original points
        await payload.update({
          collection: 'users',
          id: userId,
          data: {
            points: originalUserPoints,
            // We can't easily remove the activity from the array, so we'll leave it
            // This is a trade-off, but points accuracy is more important
          }
        })
      } catch (rollbackError) {
        console.error('Error rolling back user points:', rollbackError)
      }
    }
    
    return originalUserPoints // Return original points if there was an error
  }
}

/**
 * Calculate user points based on their activities (legacy method)
 * This is still useful for recalculating all points if needed
 */
export const calculateUserPoints = async ({
  payload,
  userId,
}: {
  payload: Payload
  userId: string
}): Promise<number> => {
  try {
    // Get user achievements and calculate points
    const userAchievements = await payload.find({
      collection: 'user-achievements',
      where: {
        user: {
          equals: userId,
        },
      },
      depth: 1,
    })

    let achievementPoints = 0
    userAchievements.docs.forEach((achievement) => {
      if (
        typeof achievement.achievement === 'object' &&
        achievement.achievement &&
        'points' in achievement.achievement
      ) {
        achievementPoints += achievement.achievement.points
      }
    })

    // Get articles by user and calculate points
    const articles = await payload.find({
      collection: 'articles',
      where: {
        author: {
          equals: userId,
        },
      },
    })

    let articlePoints = 0
    articles.docs.forEach((article) => {
      // Points for creating an article
      articlePoints += 5

      // Additional points for published articles
      if (article.status === 'published') {
        articlePoints += 15
      }

      // Points for teacher reviews (if they exist)
      if (article.teacherReview && Array.isArray(article.teacherReview)) {
        // Calculate average rating
        const ratings = article.teacherReview.map((review) => 
          typeof review === 'object' && review.rating ? review.rating : 0
        )
        
        if (ratings.length > 0) {
          const avgRating = ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length
          // Points based on average rating (1-10 scale)
          articlePoints += Math.round(avgRating * 2) // 2 points per rating point
        }
      }
      
      // Points for article views (10 points per 50 views)
      if (article.views) {
        articlePoints += Math.floor(article.views / 50) * 10
      }
    })

    // Get teacher reviews done by this user
    const reviewsCount = await payload.find({
      collection: 'articles',
      where: {
        'teacherReview.reviewer': {
          equals: userId,
        },
      },
    })

    // Points for reviews (10 points per review)
    const reviewPoints = reviewsCount.totalDocs * 10
    
    // Check if user is a mentor and calculate news points
    const user = await payload.findByID({
      collection: 'users',
      id: userId,
      depth: 1,
    });
    
    let newsPoints = 0;
    
    const userRole = typeof user.role === 'object' ? user.role?.slug : user.role;
    if (userRole === 'mentor') {
      // Get news by this mentor
      const news = await payload.find({
        collection: 'news',
        where: {
          author: {
            equals: userId,
          },
        },
      });
      
      // Points for publishing news
      newsPoints += news.totalDocs * 15;
      
      // Points for news views (20 points per 50 views)
      news.docs.forEach((newsItem) => {
        if (newsItem.views) {
          newsPoints += Math.floor(newsItem.views / 50) * 20;
        }
      });
    }

    // Calculate total points
    return achievementPoints + articlePoints + reviewPoints + newsPoints;
  } catch (error) {
    console.error('Error calculating user points:', error)
    return 0
  }
}

/**
 * Update user rankings for a specific role
 */
export const updateUserRankings = async ({
  payload,
  role = 'all'
}: {
  payload: Payload
  role?: string
}): Promise<void> => {
  try {
    // Get all users (optionally filtered by role)
    const whereClause: Where = role !== 'all' 
      ? { 'role.slug': { equals: role } }
      : {}

    const users = await payload.find({
      collection: 'users',
      where: whereClause,
      depth: 1,
      sort: '-points', // Sort by points in descending order
      limit: 1000, // Set a reasonably high limit
    })

    // Update the rank for each user
    for (let i = 0; i < users.docs.length; i++) {
      const user = users.docs[i]
      const rank = i + 1 // Rank is 1-based

      // Only update if rank has changed
      if (user.rank !== rank) {
        await payload.update({
          collection: 'users',
          id: user.id,
          data: {
            rank
          }
        })
      }
    }
  } catch (error) {
    console.error('Error updating user rankings:', error)
  }
}

/**
 * Get top users by points
 */
export const getTopUsers = async ({
  payload,
  role = 'all',
  limit = 10,
  users = null
}: {
  payload: Payload
  role?: string
  limit?: number
  users?: any[] | null
}): Promise<
  Array<{
    id: string
    name: string
    anonymizedName?: string
    points: number
    type: 'student' | 'teacher' | 'school' | 'mentor'
    rank?: number
    image?: string
  }>
> => {
  try {
    // If users are provided directly, use them instead of fetching
    if (users && Array.isArray(users) && users.length > 0) {
      return users.slice(0, limit).map((user, index) => ({
        id: user.id,
        name: user.firstName && user.lastName 
          ? `${user.firstName} ${user.lastName}`.trim() 
          : user.email 
            ? getAnonymizedName(user.email) 
            : `User ${user.id.substring(0, 4)}`,
        anonymizedName: user.email ? getAnonymizedName(user.email) : `User ${user.id.substring(0, 4)}`,
        points: user.points || 0,
        type: typeof role === 'string' ? role as any : 'student',
        rank: index + 1,
        image: user.profileImage 
          ? (typeof user.profileImage === 'object' && user.profileImage.url 
              ? user.profileImage.url
              : `/api/media/${typeof user.profileImage === 'string' ? user.profileImage : user.profileImage.id}`)
          : undefined
      }))
    }

    // Otherwise fetch users from the database
    const whereClause: Where = {}
    if (role !== 'all') {
      whereClause['role.slug'] = {
        equals: role,
      }
    }

    const fetchedUsers = await payload.find({
      collection: 'users',
      where: whereClause,
      depth: 2,
      sort: '-points',
      limit,
    })

    return fetchedUsers.docs.map((user, index) => ({
      id: user.id,
      name: user.firstName && user.lastName 
        ? `${user.firstName} ${user.lastName}`.trim() 
        : user.email 
          ? getAnonymizedName(user.email) 
          : `User ${user.id.substring(0, 4)}`,
      anonymizedName: user.email ? getAnonymizedName(user.email) : `User ${user.id.substring(0, 4)}`,
      points: user.points || 0,
      type: typeof role === 'string' ? role as any : 'student',
      rank: index + 1,
      image: user.profileImage 
        ? (typeof user.profileImage === 'object' && user.profileImage.url 
            ? user.profileImage.url
            : `/api/media/${typeof user.profileImage === 'string' ? user.profileImage : user.profileImage.id}`)
        : undefined
    }))
  } catch (error) {
    console.error('Error getting top users:', error)
    return []
  }
}

/**
 * Process article/news views for points
 */
export const processViewsForPoints = async ({ 
  payload,
  type,
  itemId,
  userId,
  currentViews
}: { 
  payload: Payload,
  type: 'article' | 'news',
  itemId: string,
  userId: string,
  currentViews: number
}): Promise<void> => {
  try {
    // Get the user to determine their role
    const user = await payload.findByID({
      collection: 'users',
      id: userId,
    });
    
    if (!user) {
      console.warn(`User not found for view points processing: ${userId}`);
      return;
    }
    
    // Get the user's role
    const userRole = typeof user.role === 'object' ? user.role?.slug : user.role;
    
    // Check if we've crossed a view milestone (every 50 views)
    const previousMilestone = Math.floor((currentViews - 1) / 50);
    const currentMilestone = Math.floor(currentViews / 50);
    
    // Add points if milestone is reached
    if (currentMilestone > previousMilestone) {
      console.log(`Milestone reached for ${type} ${itemId}: ${currentMilestone * 50} views`);
      
      // Students get 10 points per 50 article views
      if (type === 'article' && userRole === 'student') {
        await addUserPoints({
          payload,
          userId,
          type: 'article_views',
          points: 10,
          description: `Received 10 points for reaching ${currentMilestone * 50} views on article`,
          reference: itemId,
        });
        console.log(`Added 10 points to student ${userId} for article views milestone`);
      }
      
      // Mentors get 20 points per 50 news views
      if (type === 'news' && userRole === 'mentor') {
        await addUserPoints({
          payload,
          userId,
          type: 'news_views',
          points: 20,
          description: `Received 20 points for reaching ${currentMilestone * 50} views on news`,
          reference: itemId,
        });
        console.log(`Added 20 points to mentor ${userId} for news views milestone`);
      }
    }
  } catch (error) {
    console.error(`Error processing views for points: ${error}`);
  }
}

/**
 * Update all user points and top rankings in the statistics collection
 */
export const updatePointsAndRankings = async ({ 
  payload 
}: { 
  payload: Payload 
}): Promise<void> => {
  try {
    console.log("Starting to update points and rankings...");
    
    // Keep track of which operations succeeded
    const results = {
      studentRankings: false,
      teacherRankings: false,
      mentorRankings: false,
      adminRankings: false,
      schoolPoints: false
    };
    
    // Update rankings for all user types
    try {
      await updateUserRankings({ payload, role: 'student' })
      results.studentRankings = true;
    } catch (error) {
      console.error('Error updating student rankings:', error)
    }
    
    try {
      await updateUserRankings({ payload, role: 'teacher' })
      results.teacherRankings = true;
    } catch (error) {
      console.error('Error updating teacher rankings:', error)
    }
    
    try {
      await updateUserRankings({ payload, role: 'mentor' })
      results.mentorRankings = true;
    } catch (error) {
      console.error('Error updating mentor rankings:', error)
    }
    
    try {
      // Also update admin rankings
      await updateUserRankings({ payload, role: 'school-admin' })
      await updateUserRankings({ payload, role: 'super-admin' })
      results.adminRankings = true;
    } catch (error) {
      console.error('Error updating admin rankings:', error)
    }
    
    // Update school points
    try {
      await updateSchoolPoints({ payload })
      results.schoolPoints = true;
    } catch (error) {
      console.error('Error updating school points:', error)
    }
    
    // Define the user ranking type
    type UserRanking = {
      id: string;
      name: string;
      points: number;
      rank?: number;
      anonymizedName?: string;
      type?: 'student' | 'teacher' | 'mentor' | 'school';
      image?: string;
    };

    // Get top students
    let topStudents: UserRanking[] = [];
    try {
      topStudents = await getTopUsers({
        payload,
        role: 'student',
        limit: 20,
      })
      console.log(`Got ${topStudents.length} top students`);
    } catch (error) {
      console.error('Error getting top students:', error)
    }

    // Get top teachers
    let topTeachers: UserRanking[] = [];
    try {
      topTeachers = await getTopUsers({
        payload,
        role: 'teacher',
        limit: 10,
      })
      console.log(`Got ${topTeachers.length} top teachers`);
    } catch (error) {
      console.error('Error getting top teachers:', error)
    }

    // Get top mentors
    let topMentors: UserRanking[] = [];
    try {
      topMentors = await getTopUsers({
        payload,
        role: 'mentor',
        limit: 10,
      })
      console.log(`Got ${topMentors.length} top mentors`);
    } catch (error) {
      console.error('Error getting top mentors:', error)
    }

    // Update statistics collection with top users
    if (topStudents.length > 0) {
      try {
        // Find existing student ranking or create new one
        const existingStudentStats = await payload.find({
          collection: 'statistics',
          where: {
            type: {
              equals: 'studentRanking',
            },
          },
        })
        
        if (existingStudentStats.docs.length > 0) {
          // Update existing
          await payload.update({
            collection: 'statistics',
            id: existingStudentStats.docs[0].id,
            data: {
              data: topStudents,
            },
          })
        } else {
          // Create new
          await payload.create({
            collection: 'statistics',
            data: {
              name: 'Student Rankings by Points',
              type: 'studentRanking',
              data: topStudents,
            },
          })
        }
      } catch (error) {
        console.error('Error updating student rankings in statistics:', error)
      }
    }

    if (topTeachers.length > 0) {
      try {
        // Find existing teacher ranking or create new one
        const existingTeacherStats = await payload.find({
          collection: 'statistics',
          where: {
            type: {
              equals: 'teacherRanking',
            },
          },
        })
        
        if (existingTeacherStats.docs.length > 0) {
          // Update existing
          await payload.update({
            collection: 'statistics',
            id: existingTeacherStats.docs[0].id,
            data: {
              data: topTeachers,
            },
          })
        } else {
          // Create new
          await payload.create({
            collection: 'statistics',
            data: {
              name: 'Teacher Rankings by Points',
              type: 'teacherRanking',
              data: topTeachers,
            },
          })
        }
      } catch (error) {
        console.error('Error updating teacher rankings in statistics:', error)
      }
    }

    if (topMentors.length > 0) {
      try {
        // Find existing mentor ranking or create new one
        const existingMentorStats = await payload.find({
          collection: 'statistics',
          where: {
            type: {
              equals: 'mentorRanking',
            },
          },
        })
        
        if (existingMentorStats.docs.length > 0) {
          // Update existing
          await payload.update({
            collection: 'statistics',
            id: existingMentorStats.docs[0].id,
            data: {
              data: topMentors,
            },
          })
        } else {
          // Create new
          await payload.create({
            collection: 'statistics',
            data: {
              name: 'Mentor Rankings by Points',
              type: 'mentorRanking',
              data: topMentors,
            },
          })
        }
      } catch (error) {
        console.error('Error updating mentor rankings in statistics:', error)
      }
    }
    
    console.log("Completed updating points and rankings", results);
  } catch (error) {
    console.error('Error updating points and rankings:', error)
    throw new Error(`Failed to update points and rankings: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

/**
 * Calculate and update school points
 */
export const updateSchoolPoints = async ({
  payload,
  schoolId,
  incrementalUpdate = false,
  pointsChange = 0
}: {
  payload: Payload
  schoolId?: string
  incrementalUpdate?: boolean
  pointsChange?: number
}): Promise<void> => {
  try {
    // If schoolId is provided, update only that school
    // Otherwise, update all schools
    const schools = schoolId 
      ? [await payload.findByID({ collection: 'schools', id: schoolId })] 
      : (await payload.find({ collection: 'schools' })).docs
    
    for (const school of schools) {
      const schoolId = school.id
      
      // For incremental updates, we don't need to recalculate everything
      if (incrementalUpdate && pointsChange !== 0) {
        // Find existing school statistics
        const existingStats = await payload.find({
          collection: 'statistics',
          where: {
            type: { equals: 'schoolPoints' },
            school: { equals: schoolId },
          },
        })
        
        if (existingStats.docs.length > 0) {
          // Get current points
          const currentStats = existingStats.docs[0]
          // Add type guard for the data property
          const currentPoints = typeof currentStats.data === 'object' && currentStats.data !== null
            ? (Array.isArray(currentStats.data)
              ? 0 // Handle array case
              : ('points' in currentStats.data && typeof currentStats.data.points === 'number'
                ? currentStats.data.points
                : 0))
            : 0
          
          // Update incrementally with the points change
          await payload.update({
            collection: 'statistics',
            id: currentStats.id,
            data: {
              name: `${school.name} Points`,
              data: { points: currentPoints + pointsChange },
              school: schoolId,
            },
          })
          
          console.log(`Incrementally updated school ${school.name} points by ${pointsChange}`)
          continue // Skip full recalculation
        }
        // If no stats exist yet, fall through to full calculation
      }
      
      // Get all users from this school with proper pagination
      let totalPoints = 0
      let page = 1
      const limit = 1000
      let hasMore = true
      
      while (hasMore) {
        const users = await payload.find({
          collection: 'users',
          where: {
            school: { equals: schoolId },
          },
          limit,
          page,
        })
        
        // Sum points from this batch
        users.docs.forEach(user => {
          totalPoints += user.points || 0
        })
        
        // Check if we need another batch
        page += 1
        hasMore = users.docs.length === limit && users.totalDocs > (page - 1) * limit
      }
      
      // Create or update school statistics
      const existingStats = await payload.find({
        collection: 'statistics',
        where: {
          type: { equals: 'schoolPoints' },
          school: { equals: schoolId },
        },
      })
      
      if (existingStats.docs.length > 0) {
        // Update existing statistics
        await payload.update({
          collection: 'statistics',
          id: existingStats.docs[0].id,
          data: {
            name: `${school.name} Points`,
            data: { points: totalPoints },
            school: schoolId,
          },
        })
      } else {
        // Create new statistics
        await payload.create({
          collection: 'statistics',
          data: {
            name: `${school.name} Points`,
            type: 'schoolPoints',
            school: schoolId,
            data: { points: totalPoints },
          },
        })
      }
      
      console.log(`Updated school ${school.name} points to ${totalPoints}`)
    }
  } catch (error) {
    console.error('Error updating school points:', error)
    throw new Error(`Failed to update school points: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

// Define a utility function to anonymize email
const getAnonymizedName = (email: string): string => {
  if (!email) return 'Anonymous User';
  const parts = email.split('@');
  if (parts.length < 2) return 'Anonymous User';
  
  // Take first part of email and anonymize it
  const name = parts[0];
  if (name.length <= 3) return `${name}***`;
  
  return `${name.substring(0, 3)}***`;
} 