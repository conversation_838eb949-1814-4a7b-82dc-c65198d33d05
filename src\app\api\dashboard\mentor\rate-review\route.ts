import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { getPayload } from 'payload'
import { extractUserFromToken } from '@/lib/security'
import { isMediaPath } from '@/lib/media-utils'
import config from '@/payload.config'

export async function POST(request: NextRequest) {
  try {
    // Get the current user
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized: Please log in' }, { status: 401 })
    }

    const user = await extractUserFromToken(token)

    if (!user || user.role !== 'mentor') {
      return NextResponse.json(
        { error: 'Unauthorized: Only mentors can rate teacher reviews' },
        { status: 403 },
      )
    }

    // Get the request body
    const body = await request.json()
    const { articleId, reviewIndex, rating, feedback, quality } = body

    // Validate inputs
    if (!articleId || reviewIndex === undefined || !rating || !feedback || !quality) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    // Check if the article ID is a media path
    if (isMediaPath(articleId)) {
      return NextResponse.json({ error: 'Invalid article ID' }, { status: 400 })
    }

    // Validate rating
    if (rating < 1 || rating > 10 || !Number.isInteger(rating)) {
      return NextResponse.json(
        { error: 'Rating must be an integer between 1 and 10' },
        { status: 400 },
      )
    }

    // Get the article
    const payload = await getPayload({ config })
    const article = await payload.findByID({
      collection: 'articles',
      id: articleId,
    })

    if (!article) {
      return NextResponse.json({ error: 'Article not found' }, { status: 404 })
    }

    // Check if the review exists
    if (!article.teacherReview || !article.teacherReview[reviewIndex]) {
      return NextResponse.json({ error: 'Teacher review not found' }, { status: 404 })
    }

    // Create the mentor review
    const mentorReview = {
      mentor: user.id,
      rating,
      feedback,
      quality,
      createdAt: new Date().toISOString(),
    }

    // Update the article with the mentor review
    const updatedArticle = await payload.update({
      collection: 'articles',
      id: articleId,
      data: {
        teacherReview: article.teacherReview.map((review, index) => {
          if (index === reviewIndex) {
            return {
              ...review,
              mentorReview: true, // Initialize with a boolean value
            }
          }
          return review
        }),
      },
    })

    // Update teacher stats
    if (article.teacherReview[reviewIndex].reviewer) {
      const teacherId =
        typeof article.teacherReview[reviewIndex].reviewer === 'object'
          ? article.teacherReview[reviewIndex].reviewer.id
          : article.teacherReview[reviewIndex].reviewer

      if (teacherId) {
        try {
          const teacher = await payload.findByID({
            collection: 'users',
            id: teacherId,
          })

          if (teacher) {
            // Update teacher stats
            await payload.update({
              collection: 'users',
              id: teacherId,
              data: {
                stats: {
                  ...((teacher.stats as { reviewsRated?: number; totalMentorRating?: number }) ||
                    {}),
                  reviewsRated:
                    ((teacher.stats as { reviewsRated?: number })?.reviewsRated ?? 0) + 1,
                  totalMentorRating:
                    ((teacher.stats as { totalMentorRating?: number })?.totalMentorRating ?? 0) +
                    rating,
                  averageMentorRating:
                    (((teacher.stats as { totalMentorRating?: number })?.totalMentorRating ?? 0) +
                      rating) /
                    (((teacher.stats as { reviewsRated?: number })?.reviewsRated ?? 0) + 1),
                },
              },
            })
          }
        } catch (error) {
          console.error('Error updating teacher stats:', error)
          // Continue even if teacher stats update fails
        }
      }
    }

    return NextResponse.json(
      {
        success: true,
        message: 'Teacher review rated successfully',
        mentorReview,
      },
      { status: 200 },
    )
  } catch (error) {
    console.error('Error rating teacher review:', error)
    return NextResponse.json({ error: 'Failed to rate teacher review' }, { status: 500 })
  }
}
