'use client'

import { useState, useEffect } from 'react'
import { useR<PERSON><PERSON> } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Star, Eye, Clock, User, FileText } from 'lucide-react'
import { isMediaPath } from '@/lib/media-utils'

interface PendingReview {
  id: string
  articleId: string
  articleTitle: string
  teacherId: string
  teacherName: string
  rating: number
  comment: string
  approved: boolean
  createdAt: string
  reviewIndex: number
}

export default function PendingReviewsClient() {
  const router = useRouter()
  const [pendingReviews, setPendingReviews] = useState<PendingReview[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    fetchPendingReviews()
  }, [])

  const fetchPendingReviews = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/dashboard/mentor/pending-reviews', {
        credentials: 'include',
      })

      if (response.ok) {
        const data = await response.json()
        // Filter out any reviews with media path IDs
        const filteredReviews = data.reviews?.filter(
          (review: any) => review.id && !isMediaPath(review.id)
        ) || []
        setPendingReviews(filteredReviews)
      } else {
        const errorData = await response.json()
        setError(errorData.error || 'فشل في تحميل المراجعات المنتظرة')
      }
    } catch (err) {
      console.error('Error fetching pending reviews:', err)
      setError('حدث خطأ أثناء تحميل المراجعات')
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      })
    } catch {
      return 'تاريخ غير صالح'
    }
  }

  const handleReviewClick = (review: PendingReview) => {
    router.push(`/dashboard/mentor/review-feedback/${review.articleId}/${review.reviewIndex}`)
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري تحميل المراجعات...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <Card>
        <CardContent className="py-12">
          <div className="text-center">
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={fetchPendingReviews} variant="outline">
              إعادة المحاولة
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6" dir="rtl">
      {/* Summary Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Clock className="ml-2 h-5 w-5 text-blue-500" />
            ملخص المراجعات المنتظرة
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{pendingReviews.length}</div>
              <div className="text-sm text-gray-600">إجمالي المراجعات المنتظرة</div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {pendingReviews.filter(r => r.approved).length}
              </div>
              <div className="text-sm text-gray-600">مراجعات موافق عليها</div>
            </div>
            <div className="text-center p-4 bg-yellow-50 rounded-lg">
              <div className="text-2xl font-bold text-yellow-600">
                {pendingReviews.filter(r => !r.approved).length}
              </div>
              <div className="text-sm text-gray-600">مراجعات غير موافق عليها</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Reviews Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Star className="ml-2 h-5 w-5 text-yellow-500" />
            جميع المراجعات المنتظرة للتقييم
          </CardTitle>
        </CardHeader>
        <CardContent>
          {pendingReviews.length > 0 ? (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>المعلم</TableHead>
                    <TableHead>المقال</TableHead>
                    <TableHead>التقييم</TableHead>
                    <TableHead>الحالة</TableHead>
                    <TableHead>التاريخ</TableHead>
                    <TableHead>الإجراءات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {pendingReviews.map((review, index) => (
                    <TableRow key={review.id || `review-${index}`} className="hover:bg-gray-50">
                      <TableCell>
                        <div className="flex items-center space-x-3 space-x-reverse">
                          <User className="h-4 w-4 text-gray-400" />
                          <div>
                            <div className="font-medium">{review.teacherName || 'معلم غير معروف'}</div>
                            <div className="text-xs text-gray-500">
                              <Badge variant="outline" className="text-xs">
                                {review.teacherId?.substring(0, 8) || 'غير معروف'}
                              </Badge>
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <FileText className="h-4 w-4 text-gray-400" />
                          <div className="max-w-xs truncate" title={review.articleTitle}>
                            {review.articleTitle || 'مقال غير معروف'}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Star className="h-4 w-4 text-yellow-500 ml-1" />
                          <span className="font-medium">{review.rating}/10</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={review.approved ? "default" : "secondary"}>
                          {review.approved ? 'موافق عليه' : 'غير موافق عليه'}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-sm text-gray-600">
                        {formatDate(review.createdAt)}
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleReviewClick(review)}
                          className="flex items-center"
                        >
                          <Eye className="h-4 w-4 ml-1" />
                          تقييم
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <div className="text-center py-12">
              <Star className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد مراجعات منتظرة</h3>
              <p className="text-gray-600 mb-4">جميع مراجعات المعلمين تم تقييمها بالفعل</p>
              <Button
                variant="outline"
                onClick={() => router.push('/dashboard/mentor')}
              >
                العودة إلى لوحة التحكم
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
