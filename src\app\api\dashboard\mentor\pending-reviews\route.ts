import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { getPayload } from 'payload'
import { extractUserFromToken } from '@/lib/security'
import { isMediaPath, safeMediaQuery } from '@/lib/media-utils'
import payloadConfig from '@/payload.config'

export async function GET(request: NextRequest) {
  try {
    // Get the current user
    const cookieStore = await cookies()
    const token = cookieStore.get('payload-token')?.value

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized: Please log in' }, { status: 401 })
    }

    const user = await extractUserFromToken(token)

    if (!user || user.role !== 'mentor') {
      return NextResponse.json(
        { error: 'Unauthorized: Only mentors can access this endpoint' },
        { status: 403 },
      )
    }

    // Get the school ID from the user
    const schoolId = user.school

    if (!schoolId) {
      return NextResponse.json(
        { error: 'You must be associated with a school to access this endpoint' },
        { status: 400 },
      )
    }

    // Get articles with teacher reviews that haven't been rated by a mentor
    const payload = await getPayload({ config: payloadConfig })

    try {
      // First, get all published articles (we'll filter by school later)
      const articles = await payload.find({
        collection: 'articles',
        where: {
          status: { equals: 'published' },
        },
        depth: 2, // To get teacher and article details
      })

      console.log('Total articles found:', articles.docs.length)
      console.log('School ID to filter by:', schoolId)

      // Filter in JavaScript instead of MongoDB query to avoid ObjectParameterError
      const validArticles = articles.docs
        // Remove articles with media path IDs
        .filter((article) => article && article.id && !isMediaPath(article.id))
        // Filter by school
        .filter((article) => {
          if (!article.author) return false

          // Handle both populated and non-populated author
          const authorSchool =
            typeof article.author === 'object' ? (article.author as any).school : null

          // If author is populated, check the school field
          if (authorSchool) {
            const authorSchoolId =
              typeof authorSchool === 'object' ? authorSchool.id || authorSchool._id : authorSchool
            console.log(
              'Article',
              article.id,
              'author school:',
              authorSchoolId,
              'vs mentor school:',
              schoolId,
            )
            return authorSchoolId === schoolId
          }

          // If author is not populated, we can't filter by school, so include it
          console.log('Article', article.id, 'author not populated, including')
          return true
        })
        // Only include articles with teacher reviews
        .filter(
          (article) =>
            article &&
            article.teacherReview &&
            Array.isArray(article.teacherReview) &&
            article.teacherReview.length > 0,
        )

      // Extract teacher reviews that haven't been rated by a mentor
      const pendingReviews = []

      console.log('Valid articles after filtering:', validArticles.length)

      for (const article of validArticles) {
        if (article.teacherReview && article.teacherReview.length > 0) {
          console.log(
            'Processing article:',
            article.id,
            'with',
            article.teacherReview.length,
            'reviews',
          )

          for (let index = 0; index < article.teacherReview.length; index++) {
            const review = article.teacherReview[index]

            console.log('Review', index, 'for article', article.id, ':', {
              hasmentorFeedback: 'mentorFeedback' in review,
              hasMentorReview: 'mentorReview' in review,
              mentorReviewValue: review.mentorReview,
              reviewerType: typeof review.reviewer,
              reviewer: review.reviewer,
            })

            // Skip if review has mentor feedback (either format)
            if (
              ('mentorFeedback' in review &&
                review.mentorFeedback &&
                typeof review.mentorFeedback === 'object') ||
              ('mentorReview' in review && review.mentorReview === true)
            ) {
              console.log(
                'Skipping review',
                index,
                'for article',
                article.id,
                '- already has mentor feedback',
              )
              continue
            }

            // Get teacher name and ID
            let teacherName = 'Unknown Teacher'
            let teacherId = ''
            if (typeof review.reviewer === 'object' && review.reviewer) {
              const firstName = (review.reviewer as any).firstName || ''
              const lastName = (review.reviewer as any).lastName || ''
              const email = (review.reviewer as any).email || ''
              teacherName = `${firstName} ${lastName}`.trim() || email
              teacherId = (review.reviewer as any).id || (review.reviewer as any).$oid || ''
            } else {
              teacherId = review.reviewer as string
            }

            pendingReviews.push({
              id: `${article.id}-${index}`,
              articleId: article.id,
              articleTitle: article.title || 'Untitled Article',
              teacherId,
              teacherName,
              rating: review.rating || 0,
              comment: review.comment || '',
              approved: review.approved || false,
              createdAt: (review as any).createdAt || article.createdAt,
              reviewIndex: index,
            })
          }
        }
      }

      return NextResponse.json(
        {
          success: true,
          reviews: pendingReviews,
        },
        { status: 200 },
      )
    } catch (error) {
      console.error('Error fetching pending reviews:', error)
      return NextResponse.json(
        {
          error: 'Failed to fetch pending reviews',
          details: error instanceof Error ? error.message : String(error),
        },
        { status: 500 },
      )
    }
  } catch (error) {
    console.error('Error fetching pending reviews:', error)
    return NextResponse.json({ error: 'Failed to fetch pending reviews' }, { status: 500 })
  }
}
