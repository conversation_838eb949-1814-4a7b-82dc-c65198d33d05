import { redirect } from 'next/navigation'
import { cookies } from 'next/headers'
import { getPayload } from 'payload'
import { extractUserFromToken } from '@/lib/security'
import payloadConfig from '@/payload.config'
import PendingReviewsClient from '@/components/dashboard/PendingReviewsClient'

export default async function PendingReviewsPage() {
  // Get the current user
  const cookieStore = await cookies()
  const token = cookieStore.get('payload-token')?.value

  if (!token) {
    redirect('/login')
  }

  const user = await extractUserFromToken(token)

  if (!user || user.role !== 'mentor') {
    redirect('/dashboard')
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <h1 className="text-3xl font-bold text-gray-900" dir="rtl">
              مراجعات المعلمين المنتظرة للتقييم
            </h1>
            <p className="mt-2 text-gray-600" dir="rtl">
              قم بتقييم مراجعات المعلمين للحفاظ على معايير الجودة
            </p>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <PendingReviewsClient />
      </div>
    </div>
  )
}
