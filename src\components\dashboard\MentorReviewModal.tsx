'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  ThumbsUp,
  ThumbsDown,
  Star,
  User,
  FileText,
  CheckCircle,
  AlertTriangle,
} from 'lucide-react'
import { isMediaPath } from '@/lib/media-utils'

interface PendingReview {
  id: string
  articleId: string
  articleTitle: string
  teacherId: string
  teacherName: string
  rating: number
  comment: string
  approved: boolean
  createdAt: string
  reviewIndex: number
}

interface MentorReviewModalProps {
  isOpen: boolean
  onClose: () => void
  review: PendingReview | null
  onSuccess: () => void
}

export default function MentorReviewModal({
  isOpen,
  onClose,
  review,
  onSuccess,
}: MentorReviewModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState(false)
  const [mentorApproval, setMentorApproval] = useState<'approve' | 'reject' | null>(null)
  const [mentorComment, setMentorComment] = useState('')

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setMentorApproval(null)
      setMentorComment('')
      setError('')
      setSuccess(false)
    }
  }, [isOpen])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!review || !mentorApproval || !mentorComment.trim()) {
      setError('يرجى اختيار الموافقة/الرفض وإضافة تعليق')
      return
    }

    setIsSubmitting(true)
    setError('')

    try {
      // Check if the article ID is a media path
      if (isMediaPath(review.articleId)) {
        setError('معرّف المقال غير صالح')
        setIsSubmitting(false)
        return
      }

      // Use FormData for the feedback API
      const formData = new FormData()
      formData.append('rating', mentorApproval === 'approve' ? '8' : '3')
      formData.append('comment', mentorComment)

      const response = await fetch(
        `/api/feedback/review/${review.articleId}/${review.reviewIndex}?api=true`,
        {
          method: 'POST',
          body: formData,
          credentials: 'include',
        },
      )

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'فشل في إرسال تقييم المراجعة')
      }

      setSuccess(true)

      // Close modal and refresh data after a short delay
      setTimeout(() => {
        onSuccess()
        onClose()
      }, 1500)
    } catch (err) {
      console.error('Error submitting mentor review:', err)
      setError(err instanceof Error ? err.message : 'فشل في إرسال تقييم المراجعة')
    } finally {
      setIsSubmitting(false)
    }
  }

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      })
    } catch {
      return 'تاريخ غير صالح'
    }
  }

  if (success) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-md" dir="rtl">
          <div className="flex flex-col items-center justify-center py-6">
            <CheckCircle className="h-16 w-16 text-green-500 mb-4" />
            <h3 className="text-xl font-semibold text-green-700 mb-2">تم إرسال التقييم</h3>
            <p className="text-gray-500 text-center">تم إرسال تقييمك لمراجعة المعلم بنجاح</p>
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto" dir="rtl">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Star className="ml-2 h-5 w-5 text-yellow-500" />
            تقييم مراجعة المعلم
          </DialogTitle>
        </DialogHeader>

        {review && (
          <div className="space-y-6">
            {/* Article Info */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center">
                  <FileText className="ml-2 h-4 w-4" />
                  معلومات المقال
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div>
                  <span className="font-medium">العنوان: </span>
                  <span>{review.articleTitle}</span>
                </div>
                <div>
                  <span className="font-medium">تاريخ المراجعة: </span>
                  <span>{formatDate(review.createdAt)}</span>
                </div>
              </CardContent>
            </Card>

            {/* Teacher Review Info */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center">
                  <User className="ml-2 h-4 w-4" />
                  مراجعة المعلم
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <span className="font-medium">المعلم: </span>
                    <span>{review.teacherName}</span>
                  </div>
                  <Badge variant={review.approved ? 'default' : 'secondary'}>
                    {review.approved ? 'موافق عليه' : 'غير موافق عليه'}
                  </Badge>
                </div>
                <div className="flex items-center">
                  <span className="font-medium ml-2">التقييم: </span>
                  <div className="flex items-center">
                    <Star className="h-4 w-4 text-yellow-500 ml-1" />
                    <span>{review.rating}/10</span>
                  </div>
                </div>
                <div>
                  <span className="font-medium">تعليق المعلم:</span>
                  <div className="mt-2 p-3 bg-gray-50 rounded-md">
                    <p className="text-sm">{review.comment}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Mentor Review Form */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">تقييمك كمشرف</CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-4">
                  {/* Approval/Rejection */}
                  <div className="space-y-3">
                    <Label className="text-base font-medium">هل توافق على مراجعة المعلم؟</Label>
                    <div className="flex gap-4">
                      <Button
                        type="button"
                        variant={mentorApproval === 'approve' ? 'default' : 'outline'}
                        onClick={() => setMentorApproval('approve')}
                        className="flex items-center gap-2"
                      >
                        <ThumbsUp className="h-4 w-4" />
                        موافق
                      </Button>
                      <Button
                        type="button"
                        variant={mentorApproval === 'reject' ? 'destructive' : 'outline'}
                        onClick={() => setMentorApproval('reject')}
                        className="flex items-center gap-2"
                      >
                        <ThumbsDown className="h-4 w-4" />
                        غير موافق
                      </Button>
                    </div>
                  </div>

                  {/* Comment */}
                  <div className="space-y-2">
                    <Label htmlFor="mentorComment">تعليقك للمعلم *</Label>
                    <Textarea
                      id="mentorComment"
                      value={mentorComment}
                      onChange={(e) => setMentorComment(e.target.value)}
                      placeholder="قدم تعليقات بناءة للمعلم حول مراجعته..."
                      rows={4}
                      required
                    />
                  </div>

                  {/* Error Message */}
                  {error && (
                    <div className="flex items-center gap-2 text-red-600 text-sm">
                      <AlertTriangle className="h-4 w-4" />
                      {error}
                    </div>
                  )}

                  {/* Submit Buttons */}
                  <div className="flex gap-3 pt-4">
                    <Button
                      type="submit"
                      disabled={isSubmitting || !mentorApproval || !mentorComment.trim()}
                      className="flex-1"
                    >
                      {isSubmitting ? 'جاري الإرسال...' : 'إرسال التقييم'}
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={onClose}
                      disabled={isSubmitting}
                    >
                      إلغاء
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
